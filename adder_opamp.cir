* 加法运放电路仿真
* Summing Amplifier Circuit using Op-Amp

.title Summing Amplifier Circuit

* 电源定义
VCC VCC 0 DC 15V
VEE VEE 0 DC -15V

* 输入信号源
V1 IN1 0 DC 2V
V2 IN2 0 DC 3V
V3 IN3 0 DC 1V

* 运算放大器 (使用理想运放模型)
* 节点定义: 正输入端 负输入端 输出端 正电源 负电源
XOP1 0 NINV OUT VCC VEE OPAMP

* 输入电阻
R1 IN1 NINV 10k
R2 IN2 NINV 10k
R3 IN3 NINV 10k

* 反馈电阻
RF NINV OUT 10k

* 负载电阻
RL OUT 0 10k

* 理想运放子电路定义
.subckt OPAMP 1 2 3 4 5
* 节点: 1=正输入 2=负输入 3=输出 4=正电源 5=负电源
* 输入阻抗
RIN 1 2 1MEG
* 压控电压源 (增益=100000)
E1 3 0 1 2 100000
* 输出阻抗
ROUT 3 3_INT 75
* 电源限制
D1 4 3_INT DMOD
D2 3_INT 5 DMOD
.model DMOD D
.ends OPAMP

* 分析设置
.op
.dc V1 -5 5 0.1
.print dc V(OUT) V(IN1) V(IN2) V(IN3)

* 瞬态分析
.tran 0.1m 10m

.control
* 直流工作点分析
op
print V(OUT) V(IN1) V(IN2) V(IN3) V(NINV)
echo "直流工作点分析完成"
echo "输入电压: V(IN1)=" V(IN1) "V, V(IN2)=" V(IN2) "V, V(IN3)=" V(IN3) "V"
echo "输出电压: V(OUT)=" V(OUT) "V"
echo "理论输出 = -(V1+V2+V3) = -(2+3+1) = -6V"

* DC扫描分析
dc V1 -5 5 0.5
plot V(OUT) vs V(IN1)
echo "DC扫描分析完成"

* 瞬态分析
tran 0.1m 10m
plot V(OUT) V(IN1) V(IN2) V(IN3)
echo "瞬态分析完成"

.endc

.end
